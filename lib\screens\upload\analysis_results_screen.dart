import 'package:flutter/material.dart';
import 'package:fitness_ai_app/config/app_colors.dart';
import 'package:fitness_ai_app/config/app_text_styles.dart';
import 'package:fitness_ai_app/models/analysis_result.dart';
import 'package:fitness_ai_app/widgets/upload/result_metric_widget.dart';

class AnalysisResultsScreen extends StatelessWidget {
  final AnalysisResult result;
  const AnalysisResultsScreen({super.key, required this.result});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Hasil Analisis', style: AppTextStyles.headline1),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.15),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.primary, width: 2),
              ),
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: AppColors.primary, size: 40),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Analisis Selesai!',
                          style: AppTextStyles.headline2.copyWith(color: AppColors.primary),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Berikut adalah ringkasan dari sesi latihan Anda.',
                          style: AppTextStyles.bodyRegular.copyWith(color: Colors.white70),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Row(
              children: [
                Expanded(
                  child: ResultMetricWidget(
                    label: 'Squat Benar',
                    value: result.correctSquats.toString(),
                    icon: Icons.check,
                    color: Colors.greenAccent,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ResultMetricWidget(
                    label: 'Squat Salah',
                    value: result.incorrectSquats.toString(),
                    icon: Icons.close,
                    color: Colors.redAccent,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
             Row(
              children: [
                Expanded(
                  child: ResultMetricWidget(
                    label: 'Rata-rata Sudut Lutut',
                    value: '${result.avgKneeAngle.toStringAsFixed(1)}°',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ResultMetricWidget(
                    label: 'Rata-rata Sudut Pinggul',
                    value: '${result.avgHipAngle.toStringAsFixed(1)}°',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),
            Text(
              'Saran & Umpan Balik',
              style: AppTextStyles.headline2,
            ),
            const SizedBox(height: 16),
            ...result.feedback.map((item) => _buildFeedbackItem(item)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedbackItem(String text) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.lightbulb_outline, color: AppColors.primary, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(text, style: AppTextStyles.bodyRegular.copyWith(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
