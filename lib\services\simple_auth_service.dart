// Removed crypto import to avoid database issues

class SimpleAuthService {
  static final SimpleAuthService _instance = SimpleAuthService._internal();
  
  SimpleAuthService._internal();
  
  factory SimpleAuthService() => _instance;

  // In-memory storage for testing
  static final List<Map<String, dynamic>> _users = [];
  Map<String, dynamic>? _currentUser;
  
  Map<String, dynamic>? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;

  // Simple password "hashing" for testing (not secure, just for demo)
  String _hashPassword(String password) {
    // For testing purposes, just add a simple prefix
    // In production, use proper hashing like bcrypt or argon2
    return 'hashed_$password';
  }

  // Register new user
  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      print('Registration attempt for email: $email'); // Debug
      
      // Check if user already exists
      final existingUser = _users.firstWhere(
        (user) => user['email'] == email,
        orElse: () => {},
      );
      
      if (existingUser.isNotEmpty) {
        print('User already exists'); // Debug
        return {
          'success': false,
          'message': 'Email sudah terdaftar',
        };
      }

      // Hash password
      final hashedPassword = _hashPassword(password);
      print('Password hashed: $hashedPassword'); // Debug
      
      // Create user data
      final userData = {
        'id': _users.length + 1,
        'email': email,
        'password': hashedPassword,
        'name': name,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      print('Adding user data: $userData'); // Debug

      // Add user to in-memory storage
      _users.add(userData);
      print('User added. Total users: ${_users.length}'); // Debug
      
      // Remove password from user data for security
      final userWithoutPassword = Map<String, dynamic>.from(userData);
      userWithoutPassword.remove('password');
      _currentUser = userWithoutPassword;
      
      return {
        'success': true,
        'message': 'Registrasi berhasil',
        'user': userWithoutPassword,
      };
    } catch (e) {
      print('Registration error: $e'); // Debug
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Login user
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      print('Login attempt for email: $email'); // Debug
      print('Current users in memory: ${_users.length}'); // Debug
      
      // Find user in memory
      final user = _users.firstWhere(
        (user) => user['email'] == email,
        orElse: () => {},
      );
      
      print('User found: ${user.isNotEmpty}'); // Debug
      
      if (user.isEmpty) {
        print('User not found in memory'); // Debug
        return {
          'success': false,
          'message': 'Email tidak ditemukan',
        };
      }

      // Verify password
      final hashedPassword = _hashPassword(password);
      final storedPassword = user['password'];
      print('Hashed input password: $hashedPassword'); // Debug
      print('Stored password: $storedPassword'); // Debug
      
      if (storedPassword != hashedPassword) {
        print('Password mismatch'); // Debug
        return {
          'success': false,
          'message': 'Password salah',
        };
      }

      print('Login successful'); // Debug
      // Remove password from user data for security
      final userWithoutPassword = Map<String, dynamic>.from(user);
      userWithoutPassword.remove('password');
      _currentUser = userWithoutPassword;

      return {
        'success': true,
        'message': 'Login berhasil',
        'user': userWithoutPassword,
      };
    } catch (e) {
      print('Login error: $e'); // Debug logging
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Update user profile (name only)
  Future<Map<String, dynamic>> updateProfile({required String name}) async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      // Find user in memory and update name
      final userIndex = _users.indexWhere((user) => user['id'] == _currentUser!['id']);
      if (userIndex != -1) {
        _users[userIndex]['name'] = name;
        _users[userIndex]['updated_at'] = DateTime.now().toIso8601String();

        // Update current user
        _currentUser!['name'] = name;

        return {
          'success': true,
          'message': 'Profil berhasil diperbarui',
        };
      } else {
        return {
          'success': false,
          'message': 'Gagal memperbarui profil',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Update user profile
  Future<Map<String, dynamic>> updateUserProfile(Map<String, dynamic> profileData) async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      // Find user in memory and update
      final userIndex = _users.indexWhere((user) => user['id'] == _currentUser!['id']);
      if (userIndex != -1) {
        _users[userIndex].addAll(profileData);
        _users[userIndex]['updated_at'] = DateTime.now().toIso8601String();
        
        // Update current user
        _currentUser!.addAll(profileData);
        
        return {
          'success': true,
          'message': 'Profil berhasil diperbarui',
        };
      } else {
        return {
          'success': false,
          'message': 'Gagal memperbarui profil',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Get user statistics
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      // Return dummy stats for now
      return {
        'success': true,
        'stats': {
          'workoutCount': 5,
          'totalCalories': 1250,
          'progressCount': 3,
          'analysisCount': 2,
        },
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Logout user
  Future<void> logout() async {
    _currentUser = null;
  }

  // Initialize auth (simplified)
  Future<void> initializeAuth() async {
    // Simple initialization
  }
}
