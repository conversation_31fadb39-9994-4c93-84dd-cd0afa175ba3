import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';
import 'package:fitness_ai_app/config/app_colors.dart';
import 'package:fitness_ai_app/config/app_text_styles.dart';
import 'package:fitness_ai_app/widgets/auth/auth_button.dart';
import 'package:fitness_ai_app/screens/upload/analysis_results_screen.dart';
import 'package:fitness_ai_app/models/analysis_result.dart';

class UploadVideoScreen extends StatefulWidget {
  const UploadVideoScreen({super.key});

  @override
  State<UploadVideoScreen> createState() => _UploadVideoScreenState();
}

class _UploadVideoScreenState extends State<UploadVideoScreen> {
  XFile? _videoFile;
  VideoPlayerController? _videoPlayerController;
  bool _isProcessing = false;

  Future<void> _pickVideo() async {
    final ImagePicker picker = ImagePicker();
    final XFile? pickedFile = await picker.pickVideo(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _videoFile = pickedFile;
      });
      _initializeVideoPlayer();
    }
  }

  void _initializeVideoPlayer() {
    if (_videoFile == null) return;
    _videoPlayerController = VideoPlayerController.file(File(_videoFile!.path))
      ..initialize().then((_) {
        setState(() {});
        _videoPlayerController!.setLooping(true);
        _videoPlayerController!.play();
      });
  }

  Future<void> _analyzeVideo() async {
    setState(() {
      _isProcessing = true;
    });

    // Simulasi proses analisis video
    await Future.delayed(const Duration(seconds: 5));

    // Data hasil analisis dummy
    final result = AnalysisResult(
      correctSquats: 18,
      incorrectSquats: 2,
      avgKneeAngle: 85.5,
      avgHipAngle: 92.3,
      feedback: [
        'Postur punggung sudah baik!',
        'Beberapa repetisi terlalu dalam.',
        'Jaga agar lutut tidak melewati ujung jari kaki.',
      ],
    );

    setState(() {
      _isProcessing = false;
    });

    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AnalysisResultsScreen(result: result),
        ),
      );
    }
  }

  @override
  void dispose() {
    _videoPlayerController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Unggah Video', style: AppTextStyles.headline1),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              if (_isProcessing)
                const Center(
                  child: Column(
                    children: [
                      CircularProgressIndicator(color: AppColors.primary),
                      SizedBox(height: 20),
                      Text('Menganalisis video...'),
                    ],
                  ),
                )
              else
                Expanded(
                  child: _videoFile == null
                      ? _buildUploadPlaceholder()
                      : _buildVideoPreview(),
                ),
              if (!_isProcessing) const SizedBox(height: 24),
              if (!_isProcessing)
                AuthButton(
                  text: _videoFile == null ? 'Pilih Video' : 'Mulai Analisis',
                  onPressed: _videoFile == null ? _pickVideo : _analyzeVideo,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUploadPlaceholder() {
    return GestureDetector(
      onTap: _pickVideo,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.card,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.secondaryText, style: BorderStyle.solid),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.upload_file, size: 80, color: AppColors.secondaryText),
              const SizedBox(height: 16),
              Text(
                'Ketuk untuk memilih video',
                style: AppTextStyles.bodyRegular,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoPreview() {
    return AspectRatio(
      aspectRatio: _videoPlayerController!.value.aspectRatio,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: VideoPlayer(_videoPlayerController!),
      ),
    );
  }
}
