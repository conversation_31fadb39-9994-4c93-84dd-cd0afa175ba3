import 'dart:convert';
import 'package:crypto/crypto.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  // Temporarily disable database to avoid read-only errors
  // final DatabaseHelper _dbHelper = DatabaseHelper();

  AuthService._internal();

  factory AuthService() => _instance;

  // In-memory storage for testing (temporary fix)
  static final List<Map<String, dynamic>> _users = [];

  // Current user session
  Map<String, dynamic>? _currentUser;

  Map<String, dynamic>? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;

  // Session management keys (unused in in-memory version)

  // Initialize and check for existing session
  Future<void> initializeAuth() async {
    // For now, just initialize without session persistence
    // Session persistence can be added later when SharedPreferences is working
  }

  // Save session (simplified version)
  Future<void> _saveSession(Map<String, dynamic> user) async {
    // For now, just keep user in memory
    // Session persistence can be added later
  }

  // Clear session (simplified version)
  Future<void> _clearSession() async {
    // For now, just clear memory
    // Session persistence can be added later
  }

  // Hash password using SHA-256
  String _hashPassword(String password) {
    var bytes = utf8.encode(password);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Register new user (in-memory version)
  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      print('Registration attempt for email: $email'); // Debug

      // Check if user already exists in memory
      final existingUser = _users.firstWhere(
        (user) => user['email'] == email,
        orElse: () => {},
      );

      if (existingUser.isNotEmpty) {
        print('User already exists'); // Debug
        return {
          'success': false,
          'message': 'Email sudah terdaftar',
        };
      }

      // Hash password
      final hashedPassword = _hashPassword(password);
      print('Password hashed: $hashedPassword'); // Debug

      // Create user data
      final userData = {
        'id': _users.length + 1,
        'email': email,
        'password': hashedPassword,
        'name': name,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      print('Adding user data: $userData'); // Debug

      // Add user to in-memory storage
      _users.add(userData);
      print('User added. Total users: ${_users.length}'); // Debug

      // Remove password from user data for security
      final userWithoutPassword = Map<String, dynamic>.from(userData);
      userWithoutPassword.remove('password');
      _currentUser = userWithoutPassword;
      // Save session
      await _saveSession(userWithoutPassword);

      return {
        'success': true,
        'message': 'Registrasi berhasil',
        'user': userWithoutPassword,
      };
    } catch (e) {
      print('Registration error: $e'); // Debug
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Login user (in-memory version)
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      print('Login attempt for email: $email'); // Debug
      print('Current users in memory: ${_users.length}'); // Debug

      // Find user in memory
      final user = _users.firstWhere(
        (user) => user['email'] == email,
        orElse: () => {},
      );

      print('User found: ${user.isNotEmpty}'); // Debug

      if (user.isEmpty) {
        print('User not found in memory'); // Debug
        return {
          'success': false,
          'message': 'Email tidak ditemukan',
        };
      }

      // Verify password
      final hashedPassword = _hashPassword(password);
      final storedPassword = user['password'];
      print('Hashed input password: $hashedPassword'); // Debug
      print('Stored password: $storedPassword'); // Debug

      if (storedPassword != hashedPassword) {
        print('Password mismatch'); // Debug
        return {
          'success': false,
          'message': 'Password salah',
        };
      }

      print('Login successful'); // Debug
      // Remove password from user data for security
      final userWithoutPassword = Map<String, dynamic>.from(user);
      userWithoutPassword.remove('password');
      _currentUser = userWithoutPassword;
      // Save session
      await _saveSession(userWithoutPassword);

      return {
        'success': true,
        'message': 'Login berhasil',
        'user': userWithoutPassword,
      };
    } catch (e) {
      print('Login error: $e'); // Debug logging
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Logout user
  Future<void> logout() async {
    _currentUser = null;
    await _clearSession();
  }

  // Update user profile (in-memory version)
  Future<Map<String, dynamic>> updateProfile({
    required String name,
    String? newPassword,
  }) async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      // Find user in memory and update
      final userIndex = _users.indexWhere((user) => user['id'] == _currentUser!['id']);
      if (userIndex != -1) {
        _users[userIndex]['name'] = name;
        _users[userIndex]['updated_at'] = DateTime.now().toIso8601String();

        // If new password provided, hash it
        if (newPassword != null && newPassword.isNotEmpty) {
          _users[userIndex]['password'] = _hashPassword(newPassword);
        }

        // Update current user data
        _currentUser!['name'] = name;
        _currentUser!['updated_at'] = _users[userIndex]['updated_at'];

        return {
          'success': true,
          'message': 'Profil berhasil diperbarui',
          'user': _currentUser,
        };
      } else {
        return {
          'success': false,
          'message': 'Gagal memperbarui profil',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Update user profile data (for onboarding) - in-memory version
  Future<Map<String, dynamic>> updateUserProfile(Map<String, dynamic> profileData) async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      // Find user in memory and update profile data
      final userIndex = _users.indexWhere((user) => user['id'] == _currentUser!['id']);
      if (userIndex != -1) {
        _users[userIndex].addAll(profileData);
        _users[userIndex]['updated_at'] = DateTime.now().toIso8601String();

        // Update current user data with profile information
        _currentUser!.addAll(profileData);

        return {
          'success': true,
          'message': 'Profil berhasil diperbarui',
        };
      } else {
        return {
          'success': false,
          'message': 'Gagal memperbarui profil',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Get user statistics
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      // Return dummy stats for now (since we're using in-memory storage)
      return {
        'success': true,
        'stats': {
          'workoutCount': 5,
          'totalCalories': 1250,
          'progressCount': 3,
          'analysisCount': 2,
        },
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }
}
