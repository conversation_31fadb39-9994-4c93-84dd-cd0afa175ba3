import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:fitness_ai_app/services/database_helper.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  final DatabaseHelper _dbHelper = DatabaseHelper();

  AuthService._internal();

  factory AuthService() => _instance;

  // Current user session
  Map<String, dynamic>? _currentUser;

  Map<String, dynamic>? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;

  // Session management keys
  static const String _userIdKey = 'user_id';
  static const String _userEmailKey = 'user_email';
  static const String _isLoggedInKey = 'is_logged_in';

  // Initialize and check for existing session
  Future<void> initializeAuth() async {
    // For now, just initialize without session persistence
    // Session persistence can be added later when SharedPreferences is working
  }

  // Save session (simplified version)
  Future<void> _saveSession(Map<String, dynamic> user) async {
    // For now, just keep user in memory
    // Session persistence can be added later
  }

  // Clear session (simplified version)
  Future<void> _clearSession() async {
    // For now, just clear memory
    // Session persistence can be added later
  }

  // Hash password using SHA-256
  String _hashPassword(String password) {
    var bytes = utf8.encode(password);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Register new user
  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      print('Registration attempt for email: $email'); // Debug

      // Check if user already exists
      final existingUser = await _dbHelper.getUser(email);
      if (existingUser != null) {
        print('User already exists'); // Debug
        return {
          'success': false,
          'message': 'Email sudah terdaftar',
        };
      }

      // Hash password
      final hashedPassword = _hashPassword(password);
      print('Password hashed: $hashedPassword'); // Debug

      // Create user data
      final userData = {
        'email': email,
        'password': hashedPassword,
        'name': name,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      print('Inserting user data: $userData'); // Debug

      // Insert user to database
      final userId = await _dbHelper.insertUser(userData);
      print('User inserted with ID: $userId'); // Debug

      if (userId > 0) {
        // Get the created user
        final user = await _dbHelper.getUserById(userId);
        print('Retrieved user: $user'); // Debug
        if (user != null) {
          // Remove password from user data for security
          user.remove('password');
          _currentUser = user;
          // Save session
          await _saveSession(user);
        }

        return {
          'success': true,
          'message': 'Registrasi berhasil',
          'user': user,
        };
      } else {
        print('Failed to insert user'); // Debug
        return {
          'success': false,
          'message': 'Gagal membuat akun',
        };
      }
    } catch (e) {
      print('Registration error: $e'); // Debug
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Login user
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      print('Login attempt for email: $email'); // Debug

      // Force database recreation if read-only error occurs
      try {
        await _dbHelper.database;
        print('Database initialized'); // Debug
      } catch (dbError) {
        print('Database error, trying to recreate: $dbError');
        await _dbHelper.resetDatabase();
        await _dbHelper.database;
      }

      // Get user from database
      final user = await _dbHelper.getUser(email);
      print('User query result: $user'); // Debug

      if (user == null) {
        print('User not found in database'); // Debug
        return {
          'success': false,
          'message': 'Email tidak ditemukan',
        };
      }

      // Verify password
      final hashedPassword = _hashPassword(password);
      final storedPassword = user['password'];
      print('Hashed input password: $hashedPassword'); // Debug
      print('Stored password: $storedPassword'); // Debug

      if (storedPassword != hashedPassword) {
        print('Password mismatch'); // Debug
        return {
          'success': false,
          'message': 'Password salah',
        };
      }

      print('Login successful'); // Debug
      // Remove password from user data for security
      user.remove('password');
      _currentUser = user;
      // Save session
      await _saveSession(user);

      return {
        'success': true,
        'message': 'Login berhasil',
        'user': user,
      };
    } catch (e) {
      print('Login error: $e'); // Debug logging
      return {
        'success': false,
        'message': 'Terjadi kesalahan database. Silakan coba lagi.',
      };
    }
  }

  // Logout user
  Future<void> logout() async {
    _currentUser = null;
    await _clearSession();
  }

  // Update user profile
  Future<Map<String, dynamic>> updateProfile({
    required String name,
    String? newPassword,
  }) async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      final db = await _dbHelper.database;
      Map<String, dynamic> updateData = {
        'name': name,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // If new password provided, hash it
      if (newPassword != null && newPassword.isNotEmpty) {
        updateData['password'] = _hashPassword(newPassword);
      }

      final result = await db.update(
        'users',
        updateData,
        where: 'id = ?',
        whereArgs: [_currentUser!['id']],
      );

      if (result > 0) {
        // Update current user data
        _currentUser!['name'] = name;
        _currentUser!['updated_at'] = updateData['updated_at'];

        return {
          'success': true,
          'message': 'Profil berhasil diperbarui',
          'user': _currentUser,
        };
      } else {
        return {
          'success': false,
          'message': 'Gagal memperbarui profil',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Update user profile data (for onboarding)
  Future<Map<String, dynamic>> updateUserProfile(Map<String, dynamic> profileData) async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      final result = await _dbHelper.updateUserProfile(_currentUser!['id'], profileData);

      if (result > 0) {
        // Update current user data with profile information
        _currentUser!.addAll(profileData);

        return {
          'success': true,
          'message': 'Profil berhasil diperbarui',
        };
      } else {
        return {
          'success': false,
          'message': 'Gagal memperbarui profil',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Get user statistics
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      final userId = _currentUser!['id'];
      
      // Get workout count
      final workouts = await _dbHelper.getWorkouts(userId);
      final workoutCount = workouts.length;
      
      // Calculate total calories burned
      int totalCalories = 0;
      for (var workout in workouts) {
        totalCalories += (workout['calories_burned'] ?? 0) as int;
      }
      
      // Get progress entries
      final progress = await _dbHelper.getProgress(userId);
      final progressCount = progress.length;
      
      // Get AI analysis count
      final aiAnalysis = await _dbHelper.getAIAnalysis(userId);
      final analysisCount = aiAnalysis.length;

      return {
        'success': true,
        'stats': {
          'workoutCount': workoutCount,
          'totalCalories': totalCalories,
          'progressCount': progressCount,
          'analysisCount': analysisCount,
        },
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }
}
