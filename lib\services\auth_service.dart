import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:fitness_ai_app/services/database_helper.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  // Temporarily disable database to avoid read-only errors
  // final DatabaseHelper _dbHelper = DatabaseHelper();

  AuthService._internal();

  factory AuthService() => _instance;

  // In-memory storage for testing (temporary fix)
  static final List<Map<String, dynamic>> _users = [];

  // Current user session
  Map<String, dynamic>? _currentUser;

  Map<String, dynamic>? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;

  // Session management keys
  static const String _userIdKey = 'user_id';
  static const String _userEmailKey = 'user_email';
  static const String _isLoggedInKey = 'is_logged_in';

  // Initialize and check for existing session
  Future<void> initializeAuth() async {
    // For now, just initialize without session persistence
    // Session persistence can be added later when SharedPreferences is working
  }

  // Save session (simplified version)
  Future<void> _saveSession(Map<String, dynamic> user) async {
    // For now, just keep user in memory
    // Session persistence can be added later
  }

  // Clear session (simplified version)
  Future<void> _clearSession() async {
    // For now, just clear memory
    // Session persistence can be added later
  }

  // Hash password using SHA-256
  String _hashPassword(String password) {
    var bytes = utf8.encode(password);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Register new user (in-memory version)
  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      print('Registration attempt for email: $email'); // Debug

      // Check if user already exists in memory
      final existingUser = _users.firstWhere(
        (user) => user['email'] == email,
        orElse: () => {},
      );

      if (existingUser.isNotEmpty) {
        print('User already exists'); // Debug
        return {
          'success': false,
          'message': 'Email sudah terdaftar',
        };
      }

      // Hash password
      final hashedPassword = _hashPassword(password);
      print('Password hashed: $hashedPassword'); // Debug

      // Create user data
      final userData = {
        'id': _users.length + 1,
        'email': email,
        'password': hashedPassword,
        'name': name,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      print('Adding user data: $userData'); // Debug

      // Add user to in-memory storage
      _users.add(userData);
      print('User added. Total users: ${_users.length}'); // Debug

      // Remove password from user data for security
      final userWithoutPassword = Map<String, dynamic>.from(userData);
      userWithoutPassword.remove('password');
      _currentUser = userWithoutPassword;
      // Save session
      await _saveSession(userWithoutPassword);

      return {
        'success': true,
        'message': 'Registrasi berhasil',
        'user': userWithoutPassword,
      };
    } catch (e) {
      print('Registration error: $e'); // Debug
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Login user (in-memory version)
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      print('Login attempt for email: $email'); // Debug
      print('Current users in memory: ${_users.length}'); // Debug

      // Find user in memory
      final user = _users.firstWhere(
        (user) => user['email'] == email,
        orElse: () => {},
      );

      print('User found: ${user.isNotEmpty}'); // Debug

      if (user.isEmpty) {
        print('User not found in memory'); // Debug
        return {
          'success': false,
          'message': 'Email tidak ditemukan',
        };
      }

      // Verify password
      final hashedPassword = _hashPassword(password);
      final storedPassword = user['password'];
      print('Hashed input password: $hashedPassword'); // Debug
      print('Stored password: $storedPassword'); // Debug

      if (storedPassword != hashedPassword) {
        print('Password mismatch'); // Debug
        return {
          'success': false,
          'message': 'Password salah',
        };
      }

      print('Login successful'); // Debug
      // Remove password from user data for security
      final userWithoutPassword = Map<String, dynamic>.from(user);
      userWithoutPassword.remove('password');
      _currentUser = userWithoutPassword;
      // Save session
      await _saveSession(userWithoutPassword);

      return {
        'success': true,
        'message': 'Login berhasil',
        'user': userWithoutPassword,
      };
    } catch (e) {
      print('Login error: $e'); // Debug logging
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Logout user
  Future<void> logout() async {
    _currentUser = null;
    await _clearSession();
  }

  // Update user profile
  Future<Map<String, dynamic>> updateProfile({
    required String name,
    String? newPassword,
  }) async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      final db = await _dbHelper.database;
      Map<String, dynamic> updateData = {
        'name': name,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // If new password provided, hash it
      if (newPassword != null && newPassword.isNotEmpty) {
        updateData['password'] = _hashPassword(newPassword);
      }

      final result = await db.update(
        'users',
        updateData,
        where: 'id = ?',
        whereArgs: [_currentUser!['id']],
      );

      if (result > 0) {
        // Update current user data
        _currentUser!['name'] = name;
        _currentUser!['updated_at'] = updateData['updated_at'];

        return {
          'success': true,
          'message': 'Profil berhasil diperbarui',
          'user': _currentUser,
        };
      } else {
        return {
          'success': false,
          'message': 'Gagal memperbarui profil',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Update user profile data (for onboarding)
  Future<Map<String, dynamic>> updateUserProfile(Map<String, dynamic> profileData) async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      final result = await _dbHelper.updateUserProfile(_currentUser!['id'], profileData);

      if (result > 0) {
        // Update current user data with profile information
        _currentUser!.addAll(profileData);

        return {
          'success': true,
          'message': 'Profil berhasil diperbarui',
        };
      } else {
        return {
          'success': false,
          'message': 'Gagal memperbarui profil',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }

  // Get user statistics
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      if (_currentUser == null) {
        return {
          'success': false,
          'message': 'User tidak login',
        };
      }

      final userId = _currentUser!['id'];
      
      // Get workout count
      final workouts = await _dbHelper.getWorkouts(userId);
      final workoutCount = workouts.length;
      
      // Calculate total calories burned
      int totalCalories = 0;
      for (var workout in workouts) {
        totalCalories += (workout['calories_burned'] ?? 0) as int;
      }
      
      // Get progress entries
      final progress = await _dbHelper.getProgress(userId);
      final progressCount = progress.length;
      
      // Get AI analysis count
      final aiAnalysis = await _dbHelper.getAIAnalysis(userId);
      final analysisCount = aiAnalysis.length;

      return {
        'success': true,
        'stats': {
          'workoutCount': workoutCount,
          'totalCalories': totalCalories,
          'progressCount': progressCount,
          'analysisCount': analysisCount,
        },
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Terjadi kesalahan: $e',
      };
    }
  }
}
