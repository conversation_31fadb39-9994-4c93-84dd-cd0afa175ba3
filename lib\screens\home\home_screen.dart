import 'package:flutter/material.dart';
import 'package:fitness_ai_app/widgets/home/<USER>';
import 'package:fitness_ai_app/widgets/home/<USER>';
import 'package:fitness_ai_app/widgets/home/<USER>';
import 'package:fitness_ai_app/widgets/shared/user_header.dart';
import 'package:fitness_ai_app/config/app_text_styles.dart';
import 'package:fitness_ai_app/services/simple_auth_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final SimpleAuthService _authService = SimpleAuthService();
  Map<String, dynamic> _userStats = {
    'workoutCount': 0,
    'totalCalories': 0,
    'progressCount': 0,
    'analysisCount': 0,
  };
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserStats();
  }

  Future<void> _loadUserStats() async {
    final result = await _authService.getUserStats();
    if (result['success'] && mounted) {
      setState(() {
        _userStats = result['stats'];
        _isLoading = false;
      });
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const UserHeader(),
                      const SizedBox(height: 30),
                      _isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : Row(
                              children: [
                                Expanded(
                                  child: MetricCard(
                                    icon: Icons.fitness_center,
                                    value: '${_userStats['workoutCount']}',
                                    label: 'WORKOUTS',
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: MetricCard(
                                    icon: Icons.local_fire_department,
                                    value: '${_userStats['totalCalories']}',
                                    label: 'CAL BURN',
                                    unit: 'KCAL',
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: MetricCard(
                                    icon: Icons.analytics,
                                    value: '${_userStats['analysisCount']}',
                                    label: 'AI ANALYSIS',
                                  ),
                                ),
                              ],
                            ),
                      const SizedBox(height: 30),
                      const WeeklyChart(),
                      const SizedBox(height: 30),
                      Text(
                        'DAILY CHALLENGES',
                        style: AppTextStyles.bodyRegular.copyWith(
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1.5,
                        ),
                      ),
                      const SizedBox(height: 15),
                      const DailyChallengeCard(
                        imagePath: 'assets/images/squat_challenge.png',
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}